/**
 * 发货控制器
 * 负责处理发货相关的请求和响应
 * 注意：所有接口都不需要token验证，可以公开访问
 */
const BaseController = require("../../../../core/controllers/BaseController");

class ShippingController extends BaseController {
  constructor(prisma) {
    super(prisma);
  }

  /**
   * 根据订单号获取配送方式列表
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async getDeliveryMethodsByOrderNo(req, res) {
    try {
      console.log('开始获取配送方式，控制器实例:', this);
      console.log('Prisma实例:', this.prisma);

      const { orderNo } = req.params;
      const { forceMatch } = req.query; // 是否强制匹配渠道

      if (!orderNo) {
        return this.fail(res, '订单号不能为空', null, 400);
      }

      // 检查Prisma实例是否存在
      if (!this.prisma) {
        console.error('Prisma实例不存在');
        return this.fail(res, '数据库连接错误', null, 500);
      }

      // 转换强制匹配参数为布尔值
      const isForceMatch = forceMatch === 'true' || forceMatch === true;

      console.log('请求参数:', { orderNo, forceMatch, isForceMatch });

      // 首先检查配送方式表是否存在和可访问
      console.log('检查配送方式表是否存在...');
      try {
        const testQuery = await Promise.race([
          this.prisma.csm_delivery_method.findFirst({
            where: { is_enabled: 1 },
            select: { id: true, method_name: true }
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('数据库查询超时')), 5000)
          )
        ]);
        console.log('配送方式表测试查询成功:', testQuery);
      } catch (testError) {
        console.error('配送方式表访问失败:', testError);

        // 如果表不存在，返回错误信息
        if (testError.message.includes('does not exist') ||
            testError.message.includes('relation') ||
            testError.message.includes('table')) {
          return this.fail(res, '配送方式表不存在，请先创建数据库表', null, 500);
        }

        // 查询超时
        if (testError.message.includes('超时')) {
          return this.fail(res, '数据库查询超时，请稍后重试', null, 500);
        }

        // 其他数据库错误
        return this.fail(res, `数据库访问错误: ${testError.message}`, null, 500);
      }

      // 1. 根据订单号查找订单信息，获取渠道ID
      let channelId = null;

      console.log('查找订单渠道信息...');
      try {
        // 首先尝试从采购订单表查找
        const purchaseOrder = await Promise.race([
          this.prisma.purchase_order.findFirst({
            where: {
              OR: [
                { purchase_order_number: orderNo },
                { original_order_number: orderNo }
              ]
            },
            select: {
              channel_id: true,
              channel_name: true
            }
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('采购订单查询超时')), 3000)
          )
        ]);

        if (purchaseOrder && purchaseOrder.channel_id) {
          channelId = purchaseOrder.channel_id;
          console.log('从采购订单找到渠道ID:', channelId);
        }
      } catch (error) {
        console.log('采购订单查询失败:', error.message);
      }

      if (!channelId) {
        try {
          // 如果采购订单表没找到，尝试从原始订单表查找
          const originalOrder = await Promise.race([
            this.prisma.orders.findFirst({
              where: {
                OR: [
                  { id: isNaN(orderNo) ? undefined : BigInt(orderNo) },
                  { third_party_order_sn: orderNo }
                ]
              },
              select: {
                channel_id: true
              }
            }),
            new Promise((_, reject) =>
              setTimeout(() => reject(new Error('原始订单查询超时')), 3000)
            )
          ]);

          if (originalOrder && originalOrder.channel_id) {
            channelId = originalOrder.channel_id;
            console.log('从原始订单找到渠道ID:', channelId);
          }
        } catch (error) {
          console.log('原始订单查询失败:', error.message);
        }
      }

      // 如果没有找到渠道ID，使用默认值或继续处理
      if (!channelId) {
        console.log('未找到订单对应的渠道信息，使用通用配送方式');
        channelId = null; // 使用通用配送方式
      }

      // 2. 根据渠道ID和强制匹配模式查询对应的配送方式
      console.log('查询配送方式，渠道ID:', channelId, '强制匹配:', isForceMatch);

      let deliveryMethods = [];
      try {
        let whereCondition;

        if (isForceMatch && channelId) {
          // 强制匹配模式：只返回渠道ID完全匹配的配送方式
          whereCondition = {
            channel_id: channelId,
            is_enabled: 1
          };
        } else {
          // 默认模式：返回渠道匹配的和通用的配送方式
          const conditions = [{ channel_id: null }]; // 通用配送方式
          if (channelId) {
            conditions.push({ channel_id: channelId }); // 渠道专用配送方式
          }

          whereCondition = {
            OR: conditions,
            is_enabled: 1 // 只查询启用的配送方式
          };
        }

        deliveryMethods = await Promise.race([
          this.prisma.csm_delivery_method.findMany({
            where: whereCondition,
            select: {
              id: true,
              method_name: true,
              method_code: true,
              description: true,
              channel_id: true
            },
            orderBy: [
              { channel_id: 'desc' }, // 渠道专用的排在前面
              { id: 'asc' }
            ]
          }),
          new Promise((_, reject) =>
            setTimeout(() => reject(new Error('配送方式查询超时')), 5000)
          )
        ]);

        console.log('查询到配送方式数量:', deliveryMethods.length);
      } catch (error) {
        console.error('配送方式查询失败:', error);

        // 如果是表不存在的错误
        if (error.message.includes('does not exist') ||
            error.message.includes('relation') ||
            error.message.includes('table')) {
          return this.fail(res, '配送方式表不存在，请先创建数据库表', null, 500);
        }

        // 如果是查询超时
        if (error.message.includes('超时')) {
          return this.fail(res, '配送方式查询超时，请稍后重试', null, 500);
        }

        // 其他数据库错误
        return this.fail(res, `配送方式查询失败: ${error.message}`, null, 500);
      }

      // 3. 格式化返回数据
      if (deliveryMethods.length === 0) {
        return this.fail(res, '没有找到可用的配送方式，请检查数据库配置', null, 404);
      }

      const formattedMethods = deliveryMethods.map(method => ({
        id: method.id,
        methodName: method.method_name,
        methodCode: method.method_code,
        description: method.description,
        isChannelSpecific: method.channel_id !== null
      }));

      // 4. 返回标准格式响应
      this.success(res, {
        orderNo: orderNo,
        channelId: channelId ? channelId.toString() : null,
        forceMatch: isForceMatch,
        deliveryMethods: formattedMethods
      }, '获取配送方式列表成功');

    } catch (error) {
      console.error('获取配送方式列表失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, null, code);
    }
  }

  /**
   * 创建发货信息
   * @param {Object} req - 请求对象
   * @param {Object} res - 响应对象
   */
  async createShippingInfo(req, res) {
    try {
      const {
        orderNo,
        orderType = 1, // 默认为采购订单
        deliveryMethodId,
        expressCompanyName,
        expressCompanyId,
        trackingNo,
        shippingLocation,
        attachment,
        deliveryListPhoto,
        packagePhoto,
        businessContact,
        businessPhone,
        remarks
      } = req.body;

      // 参数验证
      const validation = this.validateFields(req.body, [
        'orderNo',
        'deliveryMethodId',
        'expressCompanyName',
        'expressCompanyId',
        'trackingNo',
        'shippingLocation',
        'businessContact',
        'businessPhone'
      ]);

      if (!validation.valid) {
        return this.fail(res, validation.message, null, 400);
      }

      // 检查配送方式是否存在
      const deliveryMethod = await this.prisma.csm_delivery_method.findUnique({
        where: { id: parseInt(deliveryMethodId) }
      });

      if (!deliveryMethod) {
        return this.fail(res, '配送方式不存在', null, 400);
      }

      // 创建发货信息
      const shippingInfo = await this.prisma.csm_shipping_info.create({
        data: {
          order_no: orderNo,
          order_type: parseInt(orderType),
          delivery_method_id: parseInt(deliveryMethodId),
          express_company_name: expressCompanyName,
          express_company_id: expressCompanyId,
          tracking_no: trackingNo,
          shipping_location: shippingLocation,
          attachment: attachment || null,
          delivery_list_photo: deliveryListPhoto || null,
          package_photo: packagePhoto || null,
          business_contact: businessContact,
          business_phone: businessPhone,
          remarks: remarks || null
        },
        include: {
          delivery_method: {
            select: {
              method_name: true,
              method_code: true
            }
          }
        }
      });

      // 格式化返回数据
      const formattedShippingInfo = {
        id: shippingInfo.id.toString(),
        orderNo: shippingInfo.order_no,
        orderType: shippingInfo.order_type,
        deliveryMethod: {
          id: shippingInfo.delivery_method_id,
          name: shippingInfo.delivery_method.method_name,
          code: shippingInfo.delivery_method.method_code
        },
        expressCompanyName: shippingInfo.express_company_name,
        expressCompanyId: shippingInfo.express_company_id,
        trackingNo: shippingInfo.tracking_no,
        shippingLocation: shippingInfo.shipping_location,
        attachment: shippingInfo.attachment,
        deliveryListPhoto: shippingInfo.delivery_list_photo,
        packagePhoto: shippingInfo.package_photo,
        businessContact: shippingInfo.business_contact,
        businessPhone: shippingInfo.business_phone,
        remarks: shippingInfo.remarks,
        createTime: shippingInfo.create_time,
        updateTime: shippingInfo.update_time
      };

      this.success(res, formattedShippingInfo, '创建发货信息成功');

    } catch (error) {
      console.error('创建发货信息失败:', error);
      const { message, code } = this.handleDbError(error);
      this.fail(res, message, null, code);
    }
  }
}

module.exports = ShippingController;
